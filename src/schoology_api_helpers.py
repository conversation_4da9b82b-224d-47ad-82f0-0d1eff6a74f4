import time as _time
import signal
import atexit

import requests
import schoolopy
from flask import abort
from requests_oauthlib import OAuth1

from config import *


# ------------------ SCHOOL0GY API -------------
def get_schoology_client():
    """Get authenticated schoolopy client"""
    if not (SCHO_CONSUMER_KEY and SCHO_CONSUMER_SECRET):
        abort(500, "Set SCHOOLOGY_KEY and SCHOOLOGY_SECRET env vars.")
    auth = schoolopy.Auth(SCHO_CONSUMER_KEY, SCHO_CONSUMER_SECRET)
    return schoolopy.Schoology(auth)


def oauth():
    if not (SCHO_CONSUMER_KEY and SCHO_CONSUMER_SECRET):
        abort(500, "Set SCHOOLOGY_KEY and SCHOOLOGY_SECRET env vars.")
    return OAuth1(SCHO_CONSUMER_KEY, SCHO_CONSUMER_SECRET)


def scho_get(path, params=None):
    r = requests.get(f"{SCHO_BASE}{path}", auth=oauth(), params=params, timeout=30)
    if r.status_code != 200:
        abort(502, f"Schoology API error {r.status_code} on {path}")
    return r.json()


def _cache_is_fresh(path: Path, max_age_secs: int) -> bool:
    if not path.exists():
        return False
    age = _time.time() - path.stat().st_mtime
    return age <= max_age_secs


def check_assignment_submission(assignment_id: str, section_id: str, user_id: str) -> dict:
    """
    Check if a user has submitted an assignment.
    Returns a dict:
      {
        "has_submission": bool,
        "submissions_disabled": bool,
        "error": Optional[str]
      }
    """
    try:
        r = requests.get(
            f"{SCHO_BASE}/sections/{section_id}/submissions/{assignment_id}/{user_id}",
            auth=oauth(),
            timeout=30
        )
        if r.status_code == 200:
            data = r.json()
            revisions = data.get("revision", [])
            has_submission = any(rev.get("draft", 1) == 0 for rev in revisions)
            return {
                "has_submission": has_submission,
                "submissions_disabled": False
            }
        elif r.status_code == 404:
            # No submissions found → could mean either "not submitted yet" OR "submissions disabled"
            # Schoology usually 404s when submissions are disabled entirely
            return {
                "has_submission": False,
                "submissions_disabled": True
            }
        else:
            msg = f"Error checking submission for assignment {assignment_id}: {r.status_code}"
            print(msg)
            return {
                "has_submission": False,
                "submissions_disabled": False,
                "error": msg
            }
    except Exception as e:
        msg = f"Exception checking submission for assignment {assignment_id}: {e}"
        print(msg)
        return {
            "has_submission": False,
            "submissions_disabled": False,
            "error": str(e)
        }


def load_sections_and_items():
    """
    Returns (from cache or API):
      section_id_to_name: { sid(str): "Course Title - Section Title" }
      item_id_to_section: { item_id(str): sid(str) }  # assignments + events + discussions
      assignment_submissions: { assignment_id(str): {"has_submission": bool, "checked_at": "ISO8601"} }
    Cache file format (single dict):
      {
        "section_id_to_name": {...},
        "item_id_to_section": {...},
        "assignment_submissions": {...},
        "generated_at": "ISO8601"
      }
    """
    # Try fresh cache
    try:
        cached = json.loads(CACHE_FILE.read_text())
        if isinstance(cached, dict) and "section_id_to_name" in cached and "item_id_to_section" in cached and (datetime.now() - datetime.fromisoformat(cached["generated_at"])).total_seconds() < CACHE_MAX_AGE_SECS:
            print("Loaded Schoology data from fresh cache.")
            assignment_submissions = cached.get("assignment_submissions", {})
            return cached["section_id_to_name"], cached["item_id_to_section"], assignment_submissions
    except Exception as e:
        print("Cache read failed, will rebuild:", e)

    if not SCHO_USER_UID:
        abort(500, "Set SCHOOLOGY_UID to your Schoology numeric UID (string ok).")

    # Fetch sections (active enrollments)
    secs = scho_get(f"/users/{SCHO_USER_UID}/sections")
    section_id_to_name: dict[str, str] = {}
    for s in secs.get("section", []):
        sid = str(s.get("id"))
        course_title = s.get("course_title", "") or ""
        section_title = s.get("section_title", "") or ""
        title = f"{course_title} - {section_title}".strip(" -")
        if sid:
            section_id_to_name[sid] = title

    # Build item map: assignments + events + discussions
    item_id_to_section: dict[str, str] = {}
    for sid in list(section_id_to_name.keys()):
        # assignments
        try:
            lst = scho_get(f"/sections/{sid}/assignments", params={"limit": 200})
            for a in lst.get("assignment", []):
                aid = str(a.get("id") or "")
                if aid:
                    item_id_to_section[aid] = sid
        except Exception as e:
            print(f"Assignments fetch failed for section {sid}:", e)

        # events
        try:
            evs = scho_get(f"/sections/{sid}/events", params={"limit": 200})
            for e in evs.get("event", []):
                eid = str(e.get("id") or "")
                if eid:
                    item_id_to_section[eid] = sid
        except Exception as e:
            print(f"Events fetch failed for section {sid}:", e)

        # discussions
        try:
            # Many orgs expose discussions via /discussions
            dss = scho_get(f"/sections/{sid}/discussions", params={"limit": 200})
            for d in dss.get("discussion", []):
                did = str(d.get("id") or "")
                if did:
                    item_id_to_section[did] = sid
        except Exception as e:
            print(f"Discussions fetch failed for section {sid}:", e)

    # Initialize empty assignment submissions cache
    assignment_submissions = {}

    # Write single cache dict
    cache_blob = {
        "section_id_to_name": section_id_to_name,
        "item_id_to_section": item_id_to_section,
        "assignment_submissions": assignment_submissions,
        "generated_at": datetime.now().isoformat()
    }
    try:
        CACHE_FILE.write_text(json.dumps(cache_blob, indent=2))
        print("Wrote Schoology cache:", CACHE_FILE)
    except Exception as e:
        print("Failed to write cache:", e)

    return section_id_to_name, item_id_to_section, assignment_submissions


def get_submission_status(
        item_id: str,
        due_date: datetime,
        section_id: str,
        assignment_submissions: dict,
        item_type: str = "assignment"
) -> str:
    """
    Get submission status for an assignment or discussion with caching.
    Returns:
      ✅ if submitted or manually marked as done
      ⚠️ if not submitted and not overdue (assignments only)
      ‼️ if not submitted and overdue (assignments only)
      💬 if discussion not manually marked
      - if submissions disabled
      ? if unknown/error
    Only checks API for assignments not in cache or with stale cache.
    For discussions, only checks for manual marking (no API submission check).
    """
    if not SCHO_USER_UID:
        return "?"

    overdue_symbol = "‼️" if due_date < datetime.now(tz=CURRENT_TZ) else "⚠️"

    # Check cache first for manual marking
    cached_submission = assignment_submissions.get(item_id)
    if cached_submission:
        checked_at = cached_submission.get("checked_at")
        if checked_at:
            try:
                checked_time = datetime.fromisoformat(checked_at)
                age = (_time.time() - checked_time.timestamp())
                if age <= SUBMISSION_CACHE_MAX_AGE_SECS:
                    # Cache is fresh
                    if cached_submission.get("has_submission", False):
                        return "✅"
                    elif item_type == "discussion":
                        return "💬"  # Discussion not manually marked
                    elif cached_submission.get("submissions_disabled", False):
                        return "-"
                    else:
                        return overdue_symbol
            except Exception:
                pass  # Invalid timestamp, will re-check

    # For discussions, if not in cache, just return discussion symbol
    if item_type == "discussion":
        return "💬"

    # Cache miss or stale, check API (assignments only)
    try:
        result = check_assignment_submission(item_id, section_id, SCHO_USER_UID)

        # Normalize possible return types (dict or bool)
        if isinstance(result, dict):
            if result.get("error"):
                # Don't cache errors; avoid poisoning cache with transient failures
                return "?"
            has_submission = bool(result.get("has_submission", False))
            submissions_disabled = bool(result.get("submissions_disabled", False))
        else:
            # Legacy: API returned only a boolean
            has_submission = bool(result)
            submissions_disabled = False

        # Update cache
        assignment_submissions[item_id] = {
            "has_submission": has_submission,
            "submissions_disabled": submissions_disabled,
            "checked_at": datetime.now().isoformat()
        }

        # Persist cache file (best-effort)
        try:
            if CACHE_FILE.exists():
                cached = json.loads(CACHE_FILE.read_text())
                cached["assignment_submissions"] = assignment_submissions
                CACHE_FILE.write_text(json.dumps(cached, indent=2))
        except Exception as e:
            print(f"Failed to update submission cache: {e}")

        if submissions_disabled:
            return "-"
        return "✅" if has_submission else overdue_symbol

    except Exception as e:
        print(f"Error checking submission status: {e}")
        return "?"


def mark_item_as_done(item_id: str):
    """
    Mark an assignment or discussion as manually completed by storing it in the cache.
    """
    try:
        # Load current cache
        if CACHE_FILE.exists():
            cached = json.loads(CACHE_FILE.read_text())
        else:
            cached = {
                "section_id_to_name": {},
                "item_id_to_section": {},
                "assignment_submissions": {},
                "generated_at": datetime.now().isoformat()
            }

        # Ensure assignment_submissions exists
        if "assignment_submissions" not in cached:
            cached["assignment_submissions"] = {}

        # Mark as manually completed
        cached["assignment_submissions"][item_id] = {
            "has_submission": True,
            "manually_marked": True,
            "checked_at": datetime.now().isoformat()
        }

        # Write back to cache
        CACHE_FILE.write_text(json.dumps(cached, indent=2))

        # Update in-memory cache
        ASSIGNMENT_SUBMISSIONS[item_id] = cached["assignment_submissions"][item_id]

        print(f"Marked item {item_id} as done")

    except Exception as e:
        print(f"Error marking item as done: {e}")
        raise


# Backward compatibility alias
mark_assignment_as_done = mark_item_as_done


def save_assignment_cache():
    """
    Save current in-memory assignment submissions to cache file.
    This is called when the program exits to persist any changes.
    """
    try:
        # Load current cache structure
        if CACHE_FILE.exists():
            cached = json.loads(CACHE_FILE.read_text())
        else:
            cached = {
                "section_id_to_name": {},
                "item_id_to_section": {},
                "assignment_submissions": {},
                "generated_at": datetime.now().isoformat()
            }

        # Update with current in-memory assignment submissions
        cached["assignment_submissions"] = ASSIGNMENT_SUBMISSIONS

        # Write back to cache
        CACHE_FILE.write_text(json.dumps(cached, indent=2))
        print(f"Saved assignment cache on exit: {len(ASSIGNMENT_SUBMISSIONS)} items")

    except Exception as e:
        print(f"Error saving assignment cache on exit: {e}")


def unmark_item_as_done(item_id: str):
    """
    Remove manual completion marking for an assignment or discussion.
    This allows the system to re-check the actual submission status.
    """
    try:
        # Load current cache
        if CACHE_FILE.exists():
            cached = json.loads(CACHE_FILE.read_text())
        else:
            cached = {
                "section_id_to_name": {},
                "item_id_to_section": {},
                "assignment_submissions": {},
                "generated_at": datetime.now().isoformat()
            }

        # Ensure assignment_submissions exists
        if "assignment_submissions" not in cached:
            cached["assignment_submissions"] = {}

        # Remove the item from cache (this allows re-checking actual status)
        if item_id in cached["assignment_submissions"]:
            del cached["assignment_submissions"][item_id]
            print(f"Unmarked item {item_id} as done")
        else:
            print(f"Item {item_id} was not marked as done")

        # Write back to cache
        CACHE_FILE.write_text(json.dumps(cached, indent=2))

        # Update in-memory cache
        if item_id in ASSIGNMENT_SUBMISSIONS:
            del ASSIGNMENT_SUBMISSIONS[item_id]

    except Exception as e:
        print(f"Error unmarking item as done: {e}")
        raise


# Load Schoology maps at startup
print("Building Schoology data...")
SECTION_ID_TO_NAME, ITEM_ID_TO_SECTION, ASSIGNMENT_SUBMISSIONS = load_sections_and_items()
print("Local timezone:", CURRENT_TZ)

# Register exit handlers to save cache when program exits
def signal_handler(signum, frame):
    """Handle termination signals by saving cache"""
    print(f"Received signal {signum}, saving cache...")
    save_assignment_cache()
    exit(0)

# Register handlers for common termination signals
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

# Also register with atexit as a backup
atexit.register(save_assignment_cache)
