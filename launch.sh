#!/bin/bash
# --- settings you can tweak ---
APPDIR="$HOME/PycharmProjects/schoology-ics"
PY="$HOME/PycharmProjects/ProjectNebulus/venv/bin/python"
SCRIPT="$APPDIR/src/main.py"
LOG="$APPDIR/resources/schoology-ics.log"

> $LOG

# Optional: env vars (or `source "$APPDIR/.env"`)
export SCHOOLOGY_KEY="0024503e9adf3f086623ad48c6257bac061a9b5ac"
export SCHOOLOGY_SECRET="9ae1c3a2e15d013c45c7580ac60781cd"
export SCHOOLOGY_UID="95200412"
export COURSE_DUE_TIMES_JSON='{"Bio":"8:25","Calc":"9:15","US H":"10:15","Lang":"11:05","Phys":"11:55","Mandarin":"13:35","Astro":"14:25"}'
export EMAIL="<EMAIL>"

# Avoid multiple copies if you login more than once
if pgrep -f "$SCRIPT" >/dev/null 2>&1; then
  echo "Already running" >> "$LOG"
  exit 0
fi

cd "$APPDIR" || exit 1

# Run in background, silence output, detach from Automator
nohup "$PY" "$SCRIPT" >> "$LOG" 2>&1 &

# Optionally write a timestamped breadcrumb
 echo "$(date) launched" >> "$LOG"

# Let Automator exit immediately (no window lingers)
exit 0
