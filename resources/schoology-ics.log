Tue Aug 19 09:10:01 PDT 2025 launched
Building Schoology data...
Loaded Schoology data from fresh cache.
Local timezone: PDT
 * Serving Flask app 'main'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
127.0.0.1 - - [19/Aug/2025 09:10:21] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-19 09:25:53,815] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x106f4c910>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f4c910>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 31, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f4c910>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [19/Aug/2025 09:25:53] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [19/Aug/2025 09:26:26] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-19 09:39:45,415] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x106396f50>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106396f50>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 31, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106396f50>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [19/Aug/2025 09:39:45] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [19/Aug/2025 09:40:46] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [19/Aug/2025 09:44:30] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [19/Aug/2025 09:52:34] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-19 09:56:53,909] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x106f1d060>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f1d060>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 31, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f1d060>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [19/Aug/2025 09:56:53] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [19/Aug/2025 09:57:22] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [19/Aug/2025 09:57:36] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-19 10:03:25,529] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x106396080>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106396080>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 31, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106396080>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [19/Aug/2025 10:03:25] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:09:55+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:09:55+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:25:58+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:25:58+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:39:58+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:39:58+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:44:16+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:44:16+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:52:08+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:52:08+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:57:09+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:57:09+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:57:23+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 12:57:23+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 13:03:38+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
127.0.0.1 - - [19/Aug/2025 10:04:04] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-19 12:05:20,195] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x1063971c0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x1063971c0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 31, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x1063971c0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [19/Aug/2025 12:05:20] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [19/Aug/2025 12:06:05] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 13:03:38+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 15:05:35+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 15:05:35+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 15:05:35+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2025-08-20 06:59:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2025-08-20 07:59:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/assignment/7971257700', 'SUMMARY': vText(b'Unit 1.1 Warming Up Presentation'), 'DESCRIPTION': vText(b"Step 4:\xc2\xa0Create a presentation about similarities and differences between  \\ntheir high school and Chinese high school life.  \\n\xe8\xaf\xb4\xe4\xb8\x80\xe6\xae\xb5\xe8\xaf\x9d\xef\xbc\x8c\xe6\xaf\x94\xe8\xbe\x83\xe4\xb8\xad\xe7\xbe\x8e\xe9\xab\x98\xe4\xb8\xad\xe7\x94\x9f\xe6\xb4\xbb\xe7\x9a\x84\xe4\xb8\x8d\xe5\x90\x8c\xe5\x92\x8c\xe7\x9b\xb8\xe5\x90\x8c\xe3\x80\x82\xe4\xbd\xa0\xe9\x9c\x80\xe8\xa6\x81\xe8\xaf\xb4\xe6\xbb\xa1\xe4\xb8\x80\xe5\x88\x86\xe9\x92\x9f  \\nThe script should follow the organization below with the required  \\ninformation.\\n\\n\xc2\xa0\\n\\n\xe6\x88\x91\xe7\x9a\x84\xe5\xad\xa6\xe6\xa0\xa1\xe5\x92\x8c\xe7\x94\xb5\xe5\xbd\xb1\xe9\x87\x8c\xe9\x82\xa3\xe4\xb8\xaa\xe7\xbe\x8e\xe5\x9b\xbd\xe7\x94\xb7\xe5\xad\xa9\xe5\xad\x90\xe7\x9a\x84\xe4\xb8\x80\xe6\xa0\xb7/\xe4\xb8\x8d\xe4\xb8\x80\xe6\xa0\xb7\xe3\x80\x82\\n\\n\xc2\xa0\\n\\n2. Supporting sentences about school life\\, such as daily school subjects\\,  \\nschedules\\, school activities\\, classroom arrangements\\, and after-school  \\nactivities:\\n\\n\xc2\xa0\\n\\n-\xc2\xa0\xe6\x88\x91\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe7\x82\xb9\xe8\xb5\xb7\xe5\xba\x8a\\,\xc2\xa0\xe4\xb8\x8a\xe8\xaf\xbe\xe4\xbb\xa5\xe5\x89\x8d\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(I wake up at... o'clock  \\nin the morning\\, before classes... .)  \\n-\xc2\xa0\xe6\x88\x91\xe4\xb8\x80\xe4\xb8\xaa\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x80\xe5\x85\xb1\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe9\x97\xa8\xef\xbc\x8c\xe6\x88\x91\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x80\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe8\xaf\xbe\xe3\x80\x82\xc2\xa0(I  \\nhave... subjects a week\\, on Monday I have... classes.)  \\n-\xc2\xa0\xe6\x88\x91\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe5\xbc\x80\xe5\xa7\x8b\xe4\xb8\x8a\xe8\xaf\xbe\xef\xbc\x8c\xe7\xac\xac\xe4\xb8\x80\xe8\x8a\x82\xe4\xb8\x8a\xe5\xae\x8c\xe8\xaf\xbe\xe2\x80\xa6\xe2\x80\xa6\xef\xbc\x8c\xe4\xb8\xad\xe5\x8d\x88\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(I  \\nstart classes at... in the morning\\, after the first period... \\, at noon... .)  \\n-\xc2\xa0\xe6\x97\xa9\xe4\xb8\x8a\xe7\xac\xac\xe4\xb8\x80\xe8\x8a\x82\xe2\x80\xa6\xe2\x80\xa6\xe8\x8a\x82\xe8\xaf\xbe\xef\xbc\x8c\xe8\xaf\xbe\xe5\x92\x8c\xe2\x80\xa6\xe2\x80\xa6\xe8\xaf\xbe\xe4\xb8\x8d\xe4\xb8\x80\xe6\xa0\xb7\xef\xbc\x8c\xe5\x9c\xa8\xe5\x90\x8c\xe4\xb8\x80\xe4\xb8\xaa\xe6\x95\x99\xe5\xae\xa4\xe4\xb8\x8a\xe8\xaf\xbe\xef\xbc\x8c\xe4\xb8\x8b\xe5\x8d\x88\xe6\x9c\x80\xe5\x90\x8e\xe4\xb8\x80\xe8\x8a\x82\xe2\x80\xa6\xe2\x80\xa6\xe8\x8a\x82\xe8\xaf\xbe\xef\xbc\x8c\xe4\xb8\xad\xe5\x8d\x88\xe6\x9c\x89/\xe6\xb2\xa1\xe6\x9c\x89\xe5\x8d\x88\xe4\xbc\x91\\,\xc2\xa0\xe5\xad\xa6\xe6\xa0\xa1\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe8\x80\x83\xe8\xaf\x95\xef\xbc\x8c\xe4\xbb\xa5\xe5\x90\x8e\xe6\x88\x90\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(The  \\nfirst period in the morning is... class\\, different from... class\\, held in the  \\nsame classroom\\, the last period in the afternoon is... class\\, there is/there  \\nisn't a lunch break at noon\\, the school has... exams\\, and after that... .)\\n\\n\xc2\xa0\\n\\n3. Supporting sentences about similar and different parts of school life in  \\nChina and the U.S.:**  \\n-\xc2\xa0\xe4\xb8\xad\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe2\x80\xa6\xe2\x80\xa6\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe7\x82\xb9\xe8\xb5\xb7\xe5\xba\x8a\\,\xc2\xa0\xe4\xb8\x8a\xe8\xaf\xbe\xe4\xbb\xa5\xe5\x89\x8d\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(Chinese  \\nstudents... wake up at... o'clock in the morning\\, before classes... .)  \\n-\xc2\xa0\xe5\xad\xa6\xe7\x94\x9f\xe8\xb7\x9f\xe7\xbe\x8e\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe2\x80\xa6\xe2\x80\xa6\\,\xc2\xa0\xe4\xb8\xad\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe5\x92\x8c\xe7\xbe\x8e\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe5\x91\xa8\xe6\x9c\xab\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(Students  \\nare similar/different from American students... \\, Chinese students and  \\nAmerican students on weekends... .) - Link: http://bins.schoology.com/assignment/7971257700")})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 15:22:36+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 15:22:36+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
127.0.0.1 - - [19/Aug/2025 12:23:01] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-19 13:58:39,990] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x106f4c3a0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f4c3a0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 31, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f4c3a0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [19/Aug/2025 13:58:39] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 15:22:36+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2025-08-20 06:59:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2025-08-20 07:59:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/assignment/7971257700', 'SUMMARY': vText(b'Unit 1.1 Warming Up Presentation'), 'DESCRIPTION': vText(b"Step 4:\xc2\xa0Create a presentation about similarities and differences between  \\ntheir high school and Chinese high school life.  \\n\xe8\xaf\xb4\xe4\xb8\x80\xe6\xae\xb5\xe8\xaf\x9d\xef\xbc\x8c\xe6\xaf\x94\xe8\xbe\x83\xe4\xb8\xad\xe7\xbe\x8e\xe9\xab\x98\xe4\xb8\xad\xe7\x94\x9f\xe6\xb4\xbb\xe7\x9a\x84\xe4\xb8\x8d\xe5\x90\x8c\xe5\x92\x8c\xe7\x9b\xb8\xe5\x90\x8c\xe3\x80\x82\xe4\xbd\xa0\xe9\x9c\x80\xe8\xa6\x81\xe8\xaf\xb4\xe6\xbb\xa1\xe4\xb8\x80\xe5\x88\x86\xe9\x92\x9f  \\nThe script should follow the organization below with the required  \\ninformation.\\n\\n\xc2\xa0\\n\\n\xe6\x88\x91\xe7\x9a\x84\xe5\xad\xa6\xe6\xa0\xa1\xe5\x92\x8c\xe7\x94\xb5\xe5\xbd\xb1\xe9\x87\x8c\xe9\x82\xa3\xe4\xb8\xaa\xe7\xbe\x8e\xe5\x9b\xbd\xe7\x94\xb7\xe5\xad\xa9\xe5\xad\x90\xe7\x9a\x84\xe4\xb8\x80\xe6\xa0\xb7/\xe4\xb8\x8d\xe4\xb8\x80\xe6\xa0\xb7\xe3\x80\x82\\n\\n\xc2\xa0\\n\\n2. Supporting sentences about school life\\, such as daily school subjects\\,  \\nschedules\\, school activities\\, classroom arrangements\\, and after-school  \\nactivities:\\n\\n\xc2\xa0\\n\\n-\xc2\xa0\xe6\x88\x91\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe7\x82\xb9\xe8\xb5\xb7\xe5\xba\x8a\\,\xc2\xa0\xe4\xb8\x8a\xe8\xaf\xbe\xe4\xbb\xa5\xe5\x89\x8d\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(I wake up at... o'clock  \\nin the morning\\, before classes... .)  \\n-\xc2\xa0\xe6\x88\x91\xe4\xb8\x80\xe4\xb8\xaa\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x80\xe5\x85\xb1\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe9\x97\xa8\xef\xbc\x8c\xe6\x88\x91\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x80\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe8\xaf\xbe\xe3\x80\x82\xc2\xa0(I  \\nhave... subjects a week\\, on Monday I have... classes.)  \\n-\xc2\xa0\xe6\x88\x91\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe5\xbc\x80\xe5\xa7\x8b\xe4\xb8\x8a\xe8\xaf\xbe\xef\xbc\x8c\xe7\xac\xac\xe4\xb8\x80\xe8\x8a\x82\xe4\xb8\x8a\xe5\xae\x8c\xe8\xaf\xbe\xe2\x80\xa6\xe2\x80\xa6\xef\xbc\x8c\xe4\xb8\xad\xe5\x8d\x88\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(I  \\nstart classes at... in the morning\\, after the first period... \\, at noon... .)  \\n-\xc2\xa0\xe6\x97\xa9\xe4\xb8\x8a\xe7\xac\xac\xe4\xb8\x80\xe8\x8a\x82\xe2\x80\xa6\xe2\x80\xa6\xe8\x8a\x82\xe8\xaf\xbe\xef\xbc\x8c\xe8\xaf\xbe\xe5\x92\x8c\xe2\x80\xa6\xe2\x80\xa6\xe8\xaf\xbe\xe4\xb8\x8d\xe4\xb8\x80\xe6\xa0\xb7\xef\xbc\x8c\xe5\x9c\xa8\xe5\x90\x8c\xe4\xb8\x80\xe4\xb8\xaa\xe6\x95\x99\xe5\xae\xa4\xe4\xb8\x8a\xe8\xaf\xbe\xef\xbc\x8c\xe4\xb8\x8b\xe5\x8d\x88\xe6\x9c\x80\xe5\x90\x8e\xe4\xb8\x80\xe8\x8a\x82\xe2\x80\xa6\xe2\x80\xa6\xe8\x8a\x82\xe8\xaf\xbe\xef\xbc\x8c\xe4\xb8\xad\xe5\x8d\x88\xe6\x9c\x89/\xe6\xb2\xa1\xe6\x9c\x89\xe5\x8d\x88\xe4\xbc\x91\\,\xc2\xa0\xe5\xad\xa6\xe6\xa0\xa1\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe8\x80\x83\xe8\xaf\x95\xef\xbc\x8c\xe4\xbb\xa5\xe5\x90\x8e\xe6\x88\x90\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(The  \\nfirst period in the morning is... class\\, different from... class\\, held in the  \\nsame classroom\\, the last period in the afternoon is... class\\, there is/there  \\nisn't a lunch break at noon\\, the school has... exams\\, and after that... .)\\n\\n\xc2\xa0\\n\\n3. Supporting sentences about similar and different parts of school life in  \\nChina and the U.S.:**  \\n-\xc2\xa0\xe4\xb8\xad\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe2\x80\xa6\xe2\x80\xa6\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe7\x82\xb9\xe8\xb5\xb7\xe5\xba\x8a\\,\xc2\xa0\xe4\xb8\x8a\xe8\xaf\xbe\xe4\xbb\xa5\xe5\x89\x8d\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(Chinese  \\nstudents... wake up at... o'clock in the morning\\, before classes... .)  \\n-\xc2\xa0\xe5\xad\xa6\xe7\x94\x9f\xe8\xb7\x9f\xe7\xbe\x8e\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe2\x80\xa6\xe2\x80\xa6\\,\xc2\xa0\xe4\xb8\xad\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe5\x92\x8c\xe7\xbe\x8e\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe5\x91\xa8\xe6\x9c\xab\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(Students  \\nare similar/different from American students... \\, Chinese students and  \\nAmerican students on weekends... .) - Link: http://bins.schoology.com/assignment/7971257700")})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 16:58:47+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 16:58:47+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
127.0.0.1 - - [19/Aug/2025 13:59:15] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-19 15:16:37,635] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x106f1cdc0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f1cdc0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 31, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x106f1cdc0>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [19/Aug/2025 15:16:37] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 16:58:47+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2025-08-20 06:59:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2025-08-20 07:59:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/assignment/7971257700', 'SUMMARY': vText(b'Unit 1.1 Warming Up Presentation'), 'DESCRIPTION': vText(b"Step 4:\xc2\xa0Create a presentation about similarities and differences between  \\ntheir high school and Chinese high school life.  \\n\xe8\xaf\xb4\xe4\xb8\x80\xe6\xae\xb5\xe8\xaf\x9d\xef\xbc\x8c\xe6\xaf\x94\xe8\xbe\x83\xe4\xb8\xad\xe7\xbe\x8e\xe9\xab\x98\xe4\xb8\xad\xe7\x94\x9f\xe6\xb4\xbb\xe7\x9a\x84\xe4\xb8\x8d\xe5\x90\x8c\xe5\x92\x8c\xe7\x9b\xb8\xe5\x90\x8c\xe3\x80\x82\xe4\xbd\xa0\xe9\x9c\x80\xe8\xa6\x81\xe8\xaf\xb4\xe6\xbb\xa1\xe4\xb8\x80\xe5\x88\x86\xe9\x92\x9f  \\nThe script should follow the organization below with the required  \\ninformation.\\n\\n\xc2\xa0\\n\\n\xe6\x88\x91\xe7\x9a\x84\xe5\xad\xa6\xe6\xa0\xa1\xe5\x92\x8c\xe7\x94\xb5\xe5\xbd\xb1\xe9\x87\x8c\xe9\x82\xa3\xe4\xb8\xaa\xe7\xbe\x8e\xe5\x9b\xbd\xe7\x94\xb7\xe5\xad\xa9\xe5\xad\x90\xe7\x9a\x84\xe4\xb8\x80\xe6\xa0\xb7/\xe4\xb8\x8d\xe4\xb8\x80\xe6\xa0\xb7\xe3\x80\x82\\n\\n\xc2\xa0\\n\\n2. Supporting sentences about school life\\, such as daily school subjects\\,  \\nschedules\\, school activities\\, classroom arrangements\\, and after-school  \\nactivities:\\n\\n\xc2\xa0\\n\\n-\xc2\xa0\xe6\x88\x91\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe7\x82\xb9\xe8\xb5\xb7\xe5\xba\x8a\\,\xc2\xa0\xe4\xb8\x8a\xe8\xaf\xbe\xe4\xbb\xa5\xe5\x89\x8d\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(I wake up at... o'clock  \\nin the morning\\, before classes... .)  \\n-\xc2\xa0\xe6\x88\x91\xe4\xb8\x80\xe4\xb8\xaa\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x80\xe5\x85\xb1\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe9\x97\xa8\xef\xbc\x8c\xe6\x88\x91\xe6\x98\x9f\xe6\x9c\x9f\xe4\xb8\x80\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe8\xaf\xbe\xe3\x80\x82\xc2\xa0(I  \\nhave... subjects a week\\, on Monday I have... classes.)  \\n-\xc2\xa0\xe6\x88\x91\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe5\xbc\x80\xe5\xa7\x8b\xe4\xb8\x8a\xe8\xaf\xbe\xef\xbc\x8c\xe7\xac\xac\xe4\xb8\x80\xe8\x8a\x82\xe4\xb8\x8a\xe5\xae\x8c\xe8\xaf\xbe\xe2\x80\xa6\xe2\x80\xa6\xef\xbc\x8c\xe4\xb8\xad\xe5\x8d\x88\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(I  \\nstart classes at... in the morning\\, after the first period... \\, at noon... .)  \\n-\xc2\xa0\xe6\x97\xa9\xe4\xb8\x8a\xe7\xac\xac\xe4\xb8\x80\xe8\x8a\x82\xe2\x80\xa6\xe2\x80\xa6\xe8\x8a\x82\xe8\xaf\xbe\xef\xbc\x8c\xe8\xaf\xbe\xe5\x92\x8c\xe2\x80\xa6\xe2\x80\xa6\xe8\xaf\xbe\xe4\xb8\x8d\xe4\xb8\x80\xe6\xa0\xb7\xef\xbc\x8c\xe5\x9c\xa8\xe5\x90\x8c\xe4\xb8\x80\xe4\xb8\xaa\xe6\x95\x99\xe5\xae\xa4\xe4\xb8\x8a\xe8\xaf\xbe\xef\xbc\x8c\xe4\xb8\x8b\xe5\x8d\x88\xe6\x9c\x80\xe5\x90\x8e\xe4\xb8\x80\xe8\x8a\x82\xe2\x80\xa6\xe2\x80\xa6\xe8\x8a\x82\xe8\xaf\xbe\xef\xbc\x8c\xe4\xb8\xad\xe5\x8d\x88\xe6\x9c\x89/\xe6\xb2\xa1\xe6\x9c\x89\xe5\x8d\x88\xe4\xbc\x91\\,\xc2\xa0\xe5\xad\xa6\xe6\xa0\xa1\xe6\x9c\x89\xe2\x80\xa6\xe2\x80\xa6\xe8\x80\x83\xe8\xaf\x95\xef\xbc\x8c\xe4\xbb\xa5\xe5\x90\x8e\xe6\x88\x90\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(The  \\nfirst period in the morning is... class\\, different from... class\\, held in the  \\nsame classroom\\, the last period in the afternoon is... class\\, there is/there  \\nisn't a lunch break at noon\\, the school has... exams\\, and after that... .)\\n\\n\xc2\xa0\\n\\n3. Supporting sentences about similar and different parts of school life in  \\nChina and the U.S.:**  \\n-\xc2\xa0\xe4\xb8\xad\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe2\x80\xa6\xe2\x80\xa6\xe6\x97\xa9\xe4\xb8\x8a\xe2\x80\xa6\xe2\x80\xa6\xe7\x82\xb9\xe8\xb5\xb7\xe5\xba\x8a\\,\xc2\xa0\xe4\xb8\x8a\xe8\xaf\xbe\xe4\xbb\xa5\xe5\x89\x8d\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(Chinese  \\nstudents... wake up at... o'clock in the morning\\, before classes... .)  \\n-\xc2\xa0\xe5\xad\xa6\xe7\x94\x9f\xe8\xb7\x9f\xe7\xbe\x8e\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe2\x80\xa6\xe2\x80\xa6\\,\xc2\xa0\xe4\xb8\xad\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe5\x92\x8c\xe7\xbe\x8e\xe5\x9b\xbd\xe5\xad\xa6\xe7\x94\x9f\xe5\x91\xa8\xe6\x9c\xab\xe2\x80\xa6\xe2\x80\xa6\xe3\x80\x82\xc2\xa0(Students  \\nare similar/different from American students... \\, Chinese students and  \\nAmerican students on weekends... .) - Link: http://bins.schoology.com/assignment/7971257700")})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 18:16:52+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2020-09-16 23:30:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2020-09-16 23:45:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/3067015697/profile', 'SUMMARY': vText(b'Meeting with Ms K'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/3067015697/profile')})
Skipping event without section id: VEVENT({'DTSTAMP': vDDDTypes(2025-08-19 18:16:52+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTSTART': vDDDTypes(2021-02-08 16:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'DTEND': vDDDTypes(2021-02-08 17:00:00+00:00, Parameters({'VALUE': 'DATE-TIME'})), 'UID': vText(b'<EMAIL>'), 'URL': 'http://bins.schoology.com/event/4659331369/profile', 'SUMMARY': vText(b'Latin test'), 'DESCRIPTION': vText(b' - Link: http://bins.schoology.com/event/4659331369/profile')})
127.0.0.1 - - [19/Aug/2025 15:17:21] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
