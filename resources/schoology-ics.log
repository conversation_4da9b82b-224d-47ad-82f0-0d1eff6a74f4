Sat Aug 16 23:46:19 PDT 2025 launched
Building Schoology data...
Wrote Schoology cache: schoology_cache.json
Local timezone: PDT
 * Serving Flask app 'main'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
[2025-08-17 00:02:34,844] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x1039b6a70>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x1039b6a70>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 32, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x1039b6a70>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [17/Aug/2025 00:02:34] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [17/Aug/2025 00:02:54] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
Sun Aug 17 00:14:28 PDT 2025 launched
Building Schoology data...
Loaded Schoology data from fresh cache.
Local timezone: PDT
 * Serving Flask app 'main'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
127.0.0.1 - - [17/Aug/2025 00:14:49] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
Sun Aug 17 10:10:51 PDT 2025 launched
Building Schoology data...
Loaded Schoology data from fresh cache.
Local timezone: PDT
 * Serving Flask app 'main'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
127.0.0.1 - - [17/Aug/2025 12:08:08] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 13:49:46] "GET /mark-done/7927769656 HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 13:49:59] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 14:40:32] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 14:47:57] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 14:48:06] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 15:14:36] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 15:27:03] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 16:02:24] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 16:23:14] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 17:42:56] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
[2025-08-17 18:33:26,745] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x107a82020>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x107a82020>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 32, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x107a82020>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [17/Aug/2025 18:33:26] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [17/Aug/2025 18:33:48] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 19:08:29] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 21:38:56] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 22:12:46] "GET /mark-done/7958982754 HTTP/1.1" 200 -
127.0.0.1 - - [17/Aug/2025 22:12:46] "GET /favicon.ico HTTP/1.1" 404 -
[2025-08-18 08:13:20,820] ERROR in app: Exception on / [GET]
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/socket.py", line 955, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 703, in urlopen
    httplib_response = self._make_request(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 386, in _make_request
    self._validate_conn(conn)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 1042, in _validate_conn
    conn.connect()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x107ba9600>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    retries = retries.increment(
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/urllib3/util/retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x107ba9600>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/Users/<USER>/PycharmProjects/schoology-ics/src/main.py", line 32, in proxy_ics
    r = requests.get(src, timeout=30)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/PycharmProjects/ProjectNebulus/venv/lib/python3.10/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='bins.schoology.com', port=443): Max retries exceeded with url: /calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x107ba9600>: Failed to establish a new connection: [Errno 8] nodename nor servname provided, or not known'))
127.0.0.1 - - [18/Aug/2025 08:13:20] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 500 -
127.0.0.1 - - [18/Aug/2025 08:13:41] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 09:45:01] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 09:52:02] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 09:54:37] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 13:49:28] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 14:00:01] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 14:00:07] "GET /mark-done/7927821117 HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 14:00:16] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [18/Aug/2025 14:04:43] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
