Tue Aug 19 15:39:47 PDT 2025 launched
Building Schoology data...
Loaded Schoology data from fresh cache.
Local timezone: PDT
 * Serving Flask app 'main'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
127.0.0.1 - - [19/Aug/2025 15:39:52] "GET /mark-done/7927821117 HTTP/1.1" 200 -
127.0.0.1 - - [19/Aug/2025 15:39:55] "GET /mark-done/7927769656 HTTP/1.1" 200 -
127.0.0.1 - - [19/Aug/2025 15:39:59] "GET /mark-done/7927821148 HTTP/1.1" 200 -
127.0.0.1 - - [19/Aug/2025 15:40:03] "GET /mark-done/7927821168 HTTP/1.1" 200 -
